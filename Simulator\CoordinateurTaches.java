package Simulator;

import java.util.*;
import fr.emse.fayol.maqit.simulator.components.ColorPackage;
import fr.emse.fayol.maqit.simulator.components.ColorStartZone;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;

/**
 * Auteurs: Oussama GUELFAA, Houssam ALLALI & Noa AKAYAD
 * Date: 25-05-2025
 *
 * Le coordinateur qui aide chaque robot à choisir ses tâches intelligemment.
 * On utilise un système d'enchères : les robots font des offres sur les tâches,
 * et le meilleur gagne ! C'est comme eBay mais pour des robots.
 *
 * POURQUOI ON A SIMPLIFIÉ LE CODE:
 * - Au début on avait des formules de maths super compliquées, mais on s'y perdait
 * - Maintenant on utilise juste distance + batterie + équilibrage, c'est plus clair
 * - L'efficacité des robots = juste le nombre de livraisons (simple mais ça marche)
 * - Pour les zones de transit, on a 3 règles simples au lieu d'un algorithme complexe
 * - Les enchères sont plus directes, moins de messages compliqués
 */
public class CoordinateurTaches {

    // Le robot qui possède ce coordinateur (chaque robot a le sien)
    private MyTransitRobot monRobot;

    // On garde une liste des tâches qu'on connaît, organisées par zone de départ
    private Map<String, List<Task>> tachesConnues = new HashMap<>();

    // On suit l'efficacité de chaque robot (plus il livre, plus il est efficace)
    private Map<String, Double> efficaciteRobots = new HashMap<>();
    private Map<String, Integer> livraisonsRobots = new HashMap<>();

    // Pour gérer les enchères entre robots
    private Map<String, Enchere> mesOffres = new HashMap<>();           // Les offres qu'on a faites
    private Map<String, List<Enchere>> offresRecues = new HashMap<>();  // Les offres qu'on a reçues
    private Map<String, Long> delaisEncheres = new HashMap<>();         // Quand les enchères se terminent
    private Set<String> tachesTerminees = new HashSet<>();              // Les tâches qui sont déjà finies
    private Map<String, Boolean> etatZonesTransit = new HashMap<>();    // État des zones de transit

    // Les destinations possibles pour les colis (Z1 et Z2)
    private static final Map<Integer, int[]> DESTINATIONS = new HashMap<>();
    static {
        DESTINATIONS.put(1, new int[]{5, 0});   // Z1 en haut à gauche
        DESTINATIONS.put(2, new int[]{15, 0});  // Z2 en haut à droite
    }

    // Quelques stats pour voir comment ça se passe
    private int tachesTerminees_local = 0;
    private long tempsTotal = 0;
    private int batterieUtilisee_total = 0;

    // Combien de temps on attend les offres des autres robots (en millisecondes)
    private static final long DUREE_ENCHERE = 500;

    /**
     * Classe simple pour représenter une tâche
     */
    public static class Task {
        private String id;
        private int startX, startY;
        private int goalX, goalY;
        private ColorPackage packageItem;
        private String assignedRobot;

        public Task(String id, int startX, int startY, int goalX, int goalY, ColorPackage packageItem) {
            this.id = id;
            this.startX = startX;
            this.startY = startY;
            this.goalX = goalX;
            this.goalY = goalY;
            this.packageItem = packageItem;
        }

        // Getters simples
        public String getId() { return id; }
        public int getStartX() { return startX; }
        public int getStartY() { return startY; }
        public int getGoalX() { return goalX; }
        public int getGoalY() { return goalY; }
        public ColorPackage getPackage() { return packageItem; }
        public String getAssignedRobot() { return assignedRobot; }
        public void setAssignedRobot(String robotId) { this.assignedRobot = robotId; }
    }

    // Constructeur du coordinateur
    public CoordinateurTaches(MyTransitRobot robot, ColorGridEnvironment env) {
        this.monRobot = robot;

        // Initialiser les zones de départ
        tachesConnues.put("A1", new ArrayList<>());
        tachesConnues.put("A2", new ArrayList<>());
        tachesConnues.put("A3", new ArrayList<>());

        // Initialiser les données d'efficacité pour ce robot
        String nomRobot = robot.getName();
        efficaciteRobots.put(nomRobot, 1.0);
        livraisonsRobots.put(nomRobot, 0);

        LogManager.getInstance().logCoordination(nomRobot, "Coordinateur initialisé");
    }

    public MyTransitRobot getOwnerRobot() {
        return monRobot;
    }

    // Méthode appelée quand un robot reçoit une nouvelle tâche
    public void handleNewTask(Task tache) {
        String zone = getZoneIdFromTask(tache);
        if (!tachesConnues.containsKey(zone)) {
            tachesConnues.put(zone, new ArrayList<>());
        }

        // Vérifier si on connaît déjà cette tâche
        boolean dejaConnue = false;
        for (Task t : tachesConnues.get(zone)) {
            if (t.getId().equals(tache.getId())) {
                dejaConnue = true;
                break;
            }
        }

        /: S
        if (!dejaConnue) {
            tachesConnues.get(zone).add(tache);
            LogManager.getInstance().logCoordination(monRobot.getName(), "Nouvelle tâche reçue: " + tache.getId());

            if (peutFaireOffreSurTache(tache)) {
                Enchere offre = genererOffre(tache);
                if (offre != null) {
                    mesOffres.put(tache.getId(), offre);
                    MessageOffre message = new MessageOffre(monRobot, offre);
                    monRobot.broadcastMessage(message);
                    delaisEncheres.put(tache.getId(), System.currentTimeMillis() + DUREE_ENCHERE);
                    LogManager.getInstance().logCoordination(monRobot.getName(), "Enchère de " +
                                      String.format("%.2f", offre.getBidAmount()) + " pour la tâche " + tache.getId());
                }
            }
        }
    }

    // Créer une nouvelle tâche quand un colis apparaît
    public Task createTask(ColorPackage colis, ColorStartZone zoneDepart, Map<Integer, int[]> destinations) {
        int[] destination = destinations.get(colis.getDestinationGoalId());
        if (destination == null) return null;

        String taskId = "Task-" + colis.getId();
        Task nouvelleTache = new Task(
            taskId,
            zoneDepart.getX(),
            zoneDepart.getY(),
            destination[0],
            destination[1],
            colis
        );

        String zone = colis.getStartZone();
        if (!tachesConnues.containsKey(zone)) {
            tachesConnues.put(zone, new ArrayList<>());
        }
        tachesConnues.get(zone).add(nouvelleTache);

        LogManager.getInstance().logCoordination(monRobot.getName(), "Création de la tâche " + taskId +
                          " à la zone " + zone + " avec destination " +
                          colis.getDestinationGoalId());

        NewTaskMessage message = new NewTaskMessage(monRobot, nouvelleTache);
        monRobot.broadcastMessage(message);

        return nouvelleTache;
    }

    // Méthode simple pour identifier la zone d'une tâche
    private String getZoneIdFromTask(Task tache) {
        if (tache.getStartX() == 6 && tache.getStartY() == 19) {
            return "A1";
        } else if (tache.getStartX() == 9 && tache.getStartY() == 19) {
            return "A2";
        } else if (tache.getStartX() == 12 && tache.getStartY() == 19) {
            return "A3";
        } else {
            return "A1";
        }
    }

    // Vérifier si le robot peut faire une offre sur cette tâche
    private boolean peutFaireOffreSurTache(Task tache) {
        if (monRobot.getCarriedPackage() != null) {
            LogManager.getInstance().logCoordination(monRobot.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - porte déjà un colis");
            return false;
        }

        if (monRobot.getBatteryLevel() < 15.0) {
            LogManager.getInstance().logCoordination(monRobot.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - batterie trop faible (" +
                monRobot.getBatteryLevel() + "%)");
            return false;
        }

        if (tache.getAssignedRobot() != null) {
            LogManager.getInstance().logCoordination(monRobot.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - déjà assignée à " +
                tache.getAssignedRobot());
            return false;
        }

        if (tachesTerminees.contains(tache.getId())) {
            LogManager.getInstance().logCoordination(monRobot.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - tâche déjà terminée");
            return false;
        }

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Conditions remplies pour faire une offre sur tâche " + tache.getId());
        return true;
    }

    // Générer une offre pour une tâche
    private Enchere genererOffre(Task tache) {
        double utilite = calculateUtility(monRobot, tache);

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Utilité calculée pour tâche " + tache.getId() + ": " +
            String.format("%.2f", utilite));

        if (utilite <= 0) {
            LogManager.getInstance().logCoordination(monRobot.getName(),
                "Pas d'offre pour tâche " + tache.getId() + " - utilité trop faible");
            return null;
        }

        Enchere nouvelleOffre = new Enchere(monRobot.getName(), tache.getId(), utilite);

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Offre générée pour tâche " + tache.getId() + " avec montant " +
            String.format("%.2f", utilite));

        return nouvelleOffre;
    }

    // Trouver la meilleure tâche pour ce robot
    public Task findBestTaskForRobot() {
        if (monRobot.getCarriedPackage() != null) return null;
        if (monRobot.getBatteryLevel() < 15.0) return null;

        double meilleureUtilite = -1;
        Task meilleureTache = null;

        // Regarder toutes les tâches disponibles
        for (List<Task> tachesDeLaZone : tachesConnues.values()) {
            for (Task tache : tachesDeLaZone) {
                if (tache.getAssignedRobot() != null || tachesTerminees.contains(tache.getId())) {
                    continue;
                }

                if (mesOffres.containsKey(tache.getId()) &&
                    System.currentTimeMillis() < delaisEncheres.getOrDefault(tache.getId(), 0L)) {
                    continue;
                }

                double utilite = calculateUtility(monRobot, tache);
                if (utilite > meilleureUtilite) {
                    meilleureUtilite = utilite;
                    meilleureTache = tache;
                }
            }
        }

        if (meilleureTache != null) {
            Enchere offre = new Enchere(monRobot.getName(), meilleureTache.getId(), meilleureUtilite);
            mesOffres.put(meilleureTache.getId(), offre);
            MessageOffre message = new MessageOffre(monRobot, offre);
            monRobot.broadcastMessage(message);
            delaisEncheres.put(meilleureTache.getId(), System.currentTimeMillis() + DUREE_ENCHERE);
            LogManager.getInstance().logCoordination(monRobot.getName(), "Enchère de " +
                              String.format("%.2f", meilleureUtilite) + " pour la tâche " + meilleureTache.getId());
        }

        return meilleureTache;
    }

    // Gérer une offre reçue d'un autre robot
    public void handleBid(Enchere offre) {
        String taskId = offre.getTaskId();

        Task tache = findTaskById(taskId);
        if (tache == null || tache.getAssignedRobot() != null) {
            return;
        }

        if (!offresRecues.containsKey(taskId)) {
            offresRecues.put(taskId, new ArrayList<>());
        }

        offresRecues.get(taskId).add(offre);

        Enchere notreOffre = mesOffres.get(taskId);
        if (notreOffre == null) {
            return;
        }

        if (System.currentTimeMillis() >= delaisEncheres.getOrDefault(taskId, 0L)) {
            processAuctionResults(taskId);
        }
    }

    // Calculer l'utilité d'une tâche pour un robot (version simplifiée pour étudiants)
    private double calculateUtility(MyTransitRobot robot, Task tache) {
        // Calcul simple basé sur la distance (plus proche = mieux)
        double distance = calculateDistance(robot.getX(), robot.getY(), tache.getStartX(), tache.getStartY());
        double scoreDistance = 20.0 - distance; // Score de base moins la distance

        // Bonus si le robot a assez de batterie
        double scoreBatterie = 0;
        if (robot.getBatteryLevel() > 30.0) {
            scoreBatterie = 5.0; // Bonus simple si batterie OK
        }

        // Bonus pour équilibrer le travail (robots avec moins de livraisons sont favorisés)
        int livraisons = livraisonsRobots.getOrDefault(robot.getName(), 0);
        double bonusEquilibrage = Math.max(0, 3 - livraisons); // Moins de livraisons = plus de bonus

        // Score final simple
        double scoreTotal = scoreDistance + scoreBatterie + bonusEquilibrage;

        // S'assurer que le score est positif
        return Math.max(0.1, scoreTotal);
    }

    // Calculer la distance Manhattan entre deux points
    private double calculateDistance(int x1, int y1, int x2, int y2) {
        return Math.abs(x1 - x2) + Math.abs(y1 - y2);
    }

    // Trouver une tâche par son ID
    private Task findTaskById(String taskId) {
        for (List<Task> taches : tachesConnues.values()) {
            for (Task tache : taches) {
                if (tache.getId().equals(taskId)) {
                    return tache;
                }
            }
        }
        return null;
    }

    // Traiter les résultats d'une enchère (version simplifiée pour étudiants)
    private void processAuctionResults(String taskId) {
        // Rassembler toutes les offres
        List<Enchere> toutesLesOffres = new ArrayList<>();

        if (offresRecues.containsKey(taskId)) {
            toutesLesOffres.addAll(offresRecues.get(taskId));
        }

        Enchere notreOffre = mesOffres.get(taskId);
        if (notreOffre != null) {
            toutesLesOffres.add(notreOffre);
        }

        if (toutesLesOffres.isEmpty()) {
            return;
        }

        // Trouver la meilleure offre (tri simple)
        Collections.sort(toutesLesOffres);
        Enchere offreGagnante = toutesLesOffres.get(0);
        String robotGagnant = offreGagnante.getRobotId();

        Task tache = findTaskById(taskId);
        if (tache == null) {
            return;
        }

        // Assigner la tâche au gagnant
        tache.setAssignedRobot(robotGagnant);

        if (robotGagnant.equals(monRobot.getName())) {
            // On a gagné !
            LogManager.getInstance().logCoordination(monRobot.getName(),
                "A gagné l'enchère pour la tâche " + taskId);

            // Informer les autres qu'on a gagné
            TaskAssignedMessage messageAssignation = new TaskAssignedMessage(monRobot, taskId, monRobot.getName());
            monRobot.broadcastMessage(messageAssignation);
        } else {
            // On a perdu
            LogManager.getInstance().logCoordination(monRobot.getName(),
                "A perdu l'enchère pour la tâche " + taskId + " face à " + robotGagnant);
        }

        // Nettoyer les données de cette enchère
        mesOffres.remove(taskId);
        offresRecues.remove(taskId);
        delaisEncheres.remove(taskId);
    }

    // Gérer l'acceptation d'une offre
    public void handleBidAccepted(String taskId, String robotGagnant) {
        Task tache = findTaskById(taskId);
        if (tache == null) return;

        tache.setAssignedRobot(robotGagnant);
        mesOffres.remove(taskId);
        offresRecues.remove(taskId);
        delaisEncheres.remove(taskId);

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Enchère acceptée pour la tâche " + taskId);
    }

    // Gérer le rejet d'une offre
    public void handleBidRejected(String taskId, String robotGagnant) {
        Task tache = findTaskById(taskId);
        if (tache == null) return;

        tache.setAssignedRobot(robotGagnant);
        mesOffres.remove(taskId);
        offresRecues.remove(taskId);
        delaisEncheres.remove(taskId);

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Enchère rejetée pour la tâche " + taskId + ", gagnée par " + robotGagnant);
    }

    // Gérer l'assignation d'une tâche
    public void handleTaskAssigned(String taskId, String robotAssigne) {
        Task tache = findTaskById(taskId);
        if (tache == null) return;

        tache.setAssignedRobot(robotAssigne);
        mesOffres.remove(taskId);
        offresRecues.remove(taskId);
        delaisEncheres.remove(taskId);

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Tâche " + taskId + " assignée à " + robotAssigne);
    }

    // Mettre à jour l'efficacité d'un robot (version simplifiée pour étudiants)
    public void updateRobotEfficiency(MyTransitRobot robot, long tempsDeLivraison, double batterieUtilisee) {
        String robotId = robot.getName();

        // Compter simplement le nombre de livraisons
        int livraisons = livraisonsRobots.getOrDefault(robotId, 0) + 1;
        livraisonsRobots.put(robotId, livraisons);

        // Score simple : plus de livraisons = meilleure efficacité
        double efficaciteSimple = 1.0 + (livraisons * 0.1); // Chaque livraison augmente l'efficacité
        efficaciteRobots.put(robotId, efficaciteSimple);

        // Statistiques locales simples
        tachesTerminees_local++;
        tempsTotal += tempsDeLivraison;
        batterieUtilisee_total += (int)batterieUtilisee;

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Robot " + robotId + " a terminé sa " + livraisons + "e livraison");

        // Partager l'info avec les autres robots
        EfficiencyUpdateMessage message = new EfficiencyUpdateMessage(
            monRobot, robotId, efficaciteSimple, livraisons);
        monRobot.broadcastMessage(message);
    }

    // Gérer la fin d'une tâche (version simplifiée pour étudiants)
    public void handleTaskCompleted(String robotId, String taskId, long tempsDeLivraison, double batterieUtilisee) {
        Task tache = findTaskById(taskId);
        if (tache == null) return;

        // Marquer la tâche comme terminée
        tachesTerminees.add(taskId);

        // Compter les livraisons simplement
        int livraisons = livraisonsRobots.getOrDefault(robotId, 0) + 1;
        livraisonsRobots.put(robotId, livraisons);

        // Efficacité simple basée sur le nombre de livraisons
        double efficaciteSimple = 1.0 + (livraisons * 0.1);
        efficaciteRobots.put(robotId, efficaciteSimple);

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Robot " + robotId + " a terminé la tâche " + taskId + " en " + (tempsDeLivraison/1000) +
            " secondes (livraison #" + livraisons + ")");
    }

    // Gérer la mise à jour d'efficacité
    public void handleEfficiencyUpdate(String robotId, double efficacite, int livraisons) {
        efficaciteRobots.put(robotId, efficacite);
        livraisonsRobots.put(robotId, livraisons);

        LogManager.getInstance().logCoordination(monRobot.getName(),
            "Mise à jour de l'efficacité pour " + robotId + ": " +
            String.format("%.2f", efficacite) + " (livraisons: " + livraisons + ")");
    }

    // Gérer l'état des zones de transit
    public void handleTransitZoneStatus(int x, int y, boolean estPleine) {
        String cle = x + "," + y;
        etatZonesTransit.put(cle, estPleine);

        LogManager.getInstance().logTransit(monRobot.getName(),
            "Zone de transit (" + x + "," + y + ")",
            "est " + (estPleine ? "pleine" : "disponible"));
    }

    // Décider si utiliser une zone de transit (version simplifiée pour étudiants)
    public boolean shouldUseTransitZone(MyTransitRobot robot, int destX, int destY, int transitX, int transitY) {
        // Calcul simple des distances
        double distanceDirecte = calculateDistance(robot.getX(), robot.getY(), destX, destY);
        double distanceVersTransit = calculateDistance(robot.getX(), robot.getY(), transitX, transitY);
        double distanceTransitVersDestination = calculateDistance(transitX, transitY, destX, destY);
        double distanceTotaleViaTransit = distanceVersTransit + distanceTransitVersDestination;

        // Règle simple : utiliser le transit seulement si la distance totale n'est pas trop longue
        // et si le robot a assez de batterie pour aller au transit
        boolean distanceOK = distanceTotaleViaTransit <= distanceDirecte * 1.5; // Tolérance de 50%
        boolean batterieOK = robot.getBatteryLevel() > 20.0; // Batterie minimum
        boolean distanceLongue = distanceDirecte > 8; // Utiliser transit pour les longues distances

        return distanceOK && batterieOK && distanceLongue;
    }

    // Obtenir les statistiques
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();

        stats.put("nombreTachesTerminees", tachesTerminees_local);

        long tempsMoyen = tachesTerminees_local > 0 ? tempsTotal / tachesTerminees_local : 0;
        stats.put("tempsMoyenLivraison", tempsMoyen);

        int batterieMoyenne = tachesTerminees_local > 0 ? batterieUtilisee_total / tachesTerminees_local : 0;
        stats.put("batterieMoyenneUtilisee", batterieMoyenne);

        stats.put("scoresEfficaciteRobots", new HashMap<>(efficaciteRobots));
        stats.put("nombreLivraisonsParRobot", new HashMap<>(livraisonsRobots));

        return stats;
    }
}
